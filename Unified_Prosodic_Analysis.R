# Unified Prosodic Analysis Script
# Academic-Standard Visualization of Chinese Prosodic Patterns
# Sentence: "mei-zhi mao chi-zhe yi-pen maoliang" (每只猫吃着一盆猫粮)
# Three stress conditions: Neutral, Subject stress, Object stress
#
# Based on academic standards from linguistics journals (Journal of Phonetics, Glossa)
# Author: [Your Name]
# Date: [Current Date]

# Load required libraries
library(readxl)
library(ggplot2)
library(cowplot)
library(scales)

# Set working directory to script location (optional)
# setwd(dirname(rstudioapi::getActiveDocumentContext()$path))

# Define syllable labels and positions
syllable_labels <- c("mei", "zhi", "mao", "chi", "zhe", "yi", "pen", "mao", "liang")
syllable_positions <- seq(0.5, 8.5, 1)

# Define stress regions for academic marking
subject_stress_region <- c(0, 3)    # "mei-zhi mao" (syllables 0-3)
object_stress_region <- c(5, 9)     # "yi-pen maoliang" (syllables 5-9)

# Function to create academic-standard prosodic plot
create_academic_prosody_plot <- function(data, title, stress_type = "none") {
  
  # Base plot with academic styling
  p <- ggplot(data = data, aes(x = point, y = f00)) +
    # Main F0 contour - simple black line
    geom_line(linewidth = 1.2, color = "black") +
    
    # Data points for F0 measurements
    geom_point(size = 1.5, color = "black", alpha = 0.7) +
    
    # Academic theme (black and white, clean)
    theme_bw(base_size = 12) +
    
    # Custom theme adjustments for publication
    theme(
      # Panel and background
      panel.background = element_rect(fill = "white", color = NA),
      plot.background = element_rect(fill = "white", color = NA),
      
      # Grid lines - minimal and subtle
      panel.grid.major.y = element_line(color = "grey90", linewidth = 0.3),
      panel.grid.minor = element_blank(),
      panel.grid.major.x = element_blank(),
      
      # Borders
      panel.border = element_rect(color = "black", fill = NA, linewidth = 0.8),
      
      # Axis styling
      axis.line = element_blank(),  # Border handles this
      axis.ticks = element_line(color = "black", linewidth = 0.5),
      axis.text = element_text(color = "black", size = 10),
      axis.title = element_text(color = "black", size = 12, face = "bold"),
      
      # Title styling
      plot.title = element_text(hjust = 0.5, size = 14, face = "bold", 
                               color = "black", margin = margin(b = 15)),
      
      # Margins for publication
      plot.margin = margin(15, 15, 15, 15)
    ) +
    
    # Axis configuration
    scale_x_continuous(
      limits = c(0, 9),
      breaks = seq(0, 9, 1),
      labels = rep("", 10),  # Remove default labels
      expand = c(0.02, 0.02)
    ) +
    
    scale_y_continuous(
      breaks = pretty_breaks(n = 5),
      expand = c(0.05, 0.05)
    ) +
    
    # Axis labels
    labs(
      x = "Syllable",
      y = "F0 (Hz)",
      title = title
    )
  
  # Add syllable labels
  for (i in 1:length(syllable_labels)) {
    p <- p + annotate("text",
                     x = syllable_positions[i],
                     y = min(data$f00) - 8,  # Position below the plot
                     label = syllable_labels[i],
                     vjust = 1,
                     size = 3.5,
                     color = "black",
                     fontface = "plain")
  }
  
  # Add stress marking based on condition
  if (stress_type == "subject") {
    # Mark subject stress region
    p <- p + 
      geom_vline(xintercept = subject_stress_region[1], 
                color = "black", linetype = "dashed", linewidth = 0.8, alpha = 0.7) +
      geom_vline(xintercept = subject_stress_region[2], 
                color = "black", linetype = "dashed", linewidth = 0.8, alpha = 0.7) +
      annotate("text", x = 1.5, y = max(data$f00) + 5, 
              label = "Subject Stress", size = 3, color = "black", fontface = "italic")
    
  } else if (stress_type == "object") {
    # Mark object stress region
    p <- p + 
      geom_vline(xintercept = object_stress_region[1], 
                color = "black", linetype = "dashed", linewidth = 0.8, alpha = 0.7) +
      geom_vline(xintercept = object_stress_region[2], 
                color = "black", linetype = "dashed", linewidth = 0.8, alpha = 0.7) +
      annotate("text", x = 7, y = max(data$f00) + 5, 
              label = "Object Stress", size = 3, color = "black", fontface = "italic")
  }
  
  return(p)
}

# Function to save publication-quality figures
save_academic_figure <- function(plot, filename, width = 8, height = 5) {
  # Save PNG for presentations/web
  ggsave(
    plot = plot,
    filename = paste0(filename, ".png"),
    width = width,
    height = height,
    units = "in",
    dpi = 300,
    bg = "white"
  )
  
  # Save PDF for publication
  ggsave(
    plot = plot,
    filename = paste0(filename, ".pdf"),
    width = width,
    height = height,
    units = "in",
    device = "pdf",
    bg = "white"
  )
  
  cat("Saved:", filename, ".png and .pdf\n")
}

# Main analysis function
main_analysis <- function() {
  
  cat("=== Unified Prosodic Analysis ===\n")
  cat("Generating academic-standard prosodic diagrams...\n\n")
  
  # Experiment 1: Neutral prosody (no stress)
  cat("Processing Experiment 1: Neutral prosody...\n")
  if (file.exists("Ex1/normf1.xlsx")) {
    data_ex1 <- read_excel("Ex1/normf1.xlsx")
    data_ex1$point <- as.numeric(data_ex1$point)
    
    plot_ex1 <- create_academic_prosody_plot(
      data = data_ex1,
      title = "Neutral Prosody",
      stress_type = "none"
    )
    
    save_academic_figure(plot_ex1, "Figure1_Neutral_Prosody")
    
  } else {
    cat("Warning: Ex1/normf1.xlsx not found\n")
  }
  
  # Experiment 2: Subject stress
  cat("Processing Experiment 2: Subject stress...\n")
  if (file.exists("Ex2/normf2.xlsx")) {
    data_ex2 <- read_excel("Ex2/normf2.xlsx")
    data_ex2$point <- as.numeric(data_ex2$point)
    
    plot_ex2 <- create_academic_prosody_plot(
      data = data_ex2,
      title = "Subject Stress: mei-zhi mao",
      stress_type = "subject"
    )
    
    save_academic_figure(plot_ex2, "Figure2_Subject_Stress")
    
  } else {
    cat("Warning: Ex2/normf2.xlsx not found\n")
  }
  
  # Experiment 3: Object stress
  cat("Processing Experiment 3: Object stress...\n")
  if (file.exists("Ex3/normf3.xlsx")) {
    data_ex3 <- read_excel("Ex3/normf3.xlsx")
    data_ex3$point <- as.numeric(data_ex3$point)
    
    plot_ex3 <- create_academic_prosody_plot(
      data = data_ex3,
      title = "Object Stress: yi-pen maoliang",
      stress_type = "object"
    )
    
    save_academic_figure(plot_ex3, "Figure3_Object_Stress")
    
  } else {
    cat("Warning: Ex3/normf3.xlsx not found\n")
  }
  
  cat("\n=== Analysis Complete ===\n")
  cat("Generated publication-ready figures following academic standards\n")
  cat("Files saved in current directory with both PNG and PDF formats\n")
}

# Function to create combined comparison figure
create_combined_figure <- function() {

  cat("Creating combined comparison figure...\n")

  # Check if all data files exist
  files_exist <- c(
    file.exists("Ex1/normf1.xlsx"),
    file.exists("Ex2/normf2.xlsx"),
    file.exists("Ex3/normf3.xlsx")
  )

  if (!all(files_exist)) {
    cat("Warning: Not all data files found. Skipping combined figure.\n")
    return(NULL)
  }

  # Load all datasets
  data_ex1 <- read_excel("Ex1/normf1.xlsx")
  data_ex1$point <- as.numeric(data_ex1$point)
  data_ex1$condition <- "Neutral"

  data_ex2 <- read_excel("Ex2/normf2.xlsx")
  data_ex2$point <- as.numeric(data_ex2$point)
  data_ex2$condition <- "Subject Stress"

  data_ex3 <- read_excel("Ex3/normf3.xlsx")
  data_ex3$point <- as.numeric(data_ex3$point)
  data_ex3$condition <- "Object Stress"

  # Combine datasets
  combined_data <- rbind(data_ex1, data_ex2, data_ex3)
  combined_data$condition <- factor(combined_data$condition,
                                   levels = c("Neutral", "Subject Stress", "Object Stress"))

  # Create combined plot
  p_combined <- ggplot(data = combined_data, aes(x = point, y = f00, linetype = condition)) +
    geom_line(linewidth = 1, color = "black") +

    theme_bw(base_size = 11) +
    theme(
      panel.background = element_rect(fill = "white", color = NA),
      plot.background = element_rect(fill = "white", color = NA),
      panel.grid.major.y = element_line(color = "grey90", linewidth = 0.3),
      panel.grid.minor = element_blank(),
      panel.grid.major.x = element_blank(),
      panel.border = element_rect(color = "black", fill = NA, linewidth = 0.8),
      axis.ticks = element_line(color = "black", linewidth = 0.5),
      axis.text = element_text(color = "black", size = 9),
      axis.title = element_text(color = "black", size = 11, face = "bold"),
      plot.title = element_text(hjust = 0.5, size = 13, face = "bold",
                               color = "black", margin = margin(b = 15)),
      legend.position = "bottom",
      legend.title = element_text(size = 10, face = "bold"),
      legend.text = element_text(size = 9),
      plot.margin = margin(15, 15, 15, 15)
    ) +

    scale_x_continuous(
      limits = c(0, 9),
      breaks = seq(0, 9, 1),
      labels = rep("", 10),
      expand = c(0.02, 0.02)
    ) +

    scale_y_continuous(
      breaks = pretty_breaks(n = 5),
      expand = c(0.05, 0.05)
    ) +

    scale_linetype_manual(
      name = "Stress Condition",
      values = c("Neutral" = "solid", "Subject Stress" = "dashed", "Object Stress" = "dotted")
    ) +

    labs(
      x = "Syllable",
      y = "F0 (Hz)",
      title = "Prosodic Contours: Stress Pattern Comparison"
    )

  # Add syllable labels
  for (i in 1:length(syllable_labels)) {
    p_combined <- p_combined + annotate("text",
                                       x = syllable_positions[i],
                                       y = min(combined_data$f00) - 8,
                                       label = syllable_labels[i],
                                       vjust = 1,
                                       size = 3,
                                       color = "black",
                                       fontface = "plain")
  }

  save_academic_figure(p_combined, "Figure4_Combined_Comparison", width = 10, height = 6)

  return(p_combined)
}

# Function to generate academic summary report
generate_academic_report <- function() {

  cat("\n=== ACADEMIC PROSODIC ANALYSIS REPORT ===\n")
  cat("Sentence: 每只猫吃着一盆猫粮 (mei-zhi mao chi-zhe yi-pen maoliang)\n")
  cat("Translation: 'Every cat eats a bowl of cat food'\n\n")

  cat("EXPERIMENTAL CONDITIONS:\n")
  cat("1. Neutral Prosody: No specific stress pattern\n")
  cat("2. Subject Stress: Emphasis on 'mei-zhi mao' (every cat)\n")
  cat("3. Object Stress: Emphasis on 'yi-pen maoliang' (a bowl of cat food)\n\n")

  cat("ACADEMIC STANDARDS IMPLEMENTED:\n")
  cat("- Clean black and white visualization following Journal of Phonetics conventions\n")
  cat("- Standardized axis labeling (Syllable vs F0 in Hz)\n")
  cat("- Publication-ready figure dimensions and resolution\n")
  cat("- Consistent typography and professional styling\n")
  cat("- Clear stress marking using dashed vertical lines\n")
  cat("- Both PNG (300 DPI) and PDF formats for different publication needs\n\n")

  cat("OUTPUT FILES:\n")
  cat("- Figure1_Neutral_Prosody.png/.pdf\n")
  cat("- Figure2_Subject_Stress.png/.pdf\n")
  cat("- Figure3_Object_Stress.png/.pdf\n")
  cat("- Figure4_Combined_Comparison.png/.pdf\n\n")

  cat("CITATION RECOMMENDATION:\n")
  cat("These figures follow visualization standards established in:\n")
  cat("- Journal of Phonetics\n")
  cat("- Glossa: a journal of general linguistics\n")
  cat("- Laboratory Phonology\n\n")

  cat("For academic publication, consider citing relevant prosodic analysis methods\n")
  cat("and ensure compliance with target journal's figure formatting requirements.\n")
}

# Enhanced main analysis function
main_analysis <- function() {

  cat("=== UNIFIED PROSODIC ANALYSIS ===\n")
  cat("Academic-Standard Visualization of Chinese Prosodic Patterns\n")
  cat("Following conventions from peer-reviewed linguistics journals\n\n")

  # Individual experiment analyses
  cat("Processing individual experiments...\n")

  # Experiment 1: Neutral prosody
  if (file.exists("Ex1/normf1.xlsx")) {
    cat("✓ Experiment 1: Neutral prosody\n")
    data_ex1 <- read_excel("Ex1/normf1.xlsx")
    data_ex1$point <- as.numeric(data_ex1$point)

    plot_ex1 <- create_academic_prosody_plot(
      data = data_ex1,
      title = "Neutral Prosody",
      stress_type = "none"
    )

    save_academic_figure(plot_ex1, "Figure1_Neutral_Prosody")

  } else {
    cat("✗ Ex1/normf1.xlsx not found\n")
  }

  # Experiment 2: Subject stress
  if (file.exists("Ex2/normf2.xlsx")) {
    cat("✓ Experiment 2: Subject stress\n")
    data_ex2 <- read_excel("Ex2/normf2.xlsx")
    data_ex2$point <- as.numeric(data_ex2$point)

    plot_ex2 <- create_academic_prosody_plot(
      data = data_ex2,
      title = "Subject Stress: mei-zhi mao",
      stress_type = "subject"
    )

    save_academic_figure(plot_ex2, "Figure2_Subject_Stress")

  } else {
    cat("✗ Ex2/normf2.xlsx not found\n")
  }

  # Experiment 3: Object stress
  if (file.exists("Ex3/normf3.xlsx")) {
    cat("✓ Experiment 3: Object stress\n")
    data_ex3 <- read_excel("Ex3/normf3.xlsx")
    data_ex3$point <- as.numeric(data_ex3$point)

    plot_ex3 <- create_academic_prosody_plot(
      data = data_ex3,
      title = "Object Stress: yi-pen maoliang",
      stress_type = "object"
    )

    save_academic_figure(plot_ex3, "Figure3_Object_Stress")

  } else {
    cat("✗ Ex3/normf3.xlsx not found\n")
  }

  # Create combined comparison figure
  create_combined_figure()

  # Generate academic report
  generate_academic_report()

  cat("\n=== ANALYSIS COMPLETE ===\n")
  cat("All figures generated following academic publication standards.\n")
  cat("Ready for submission to linguistics journals.\n")
}

# Execute the analysis
main_analysis()
